import 'package:flutter/material.dart';
import 'robust_network_image.dart';

class ExplorePostCard extends StatelessWidget {
  final Map<String, String> postData;

  const ExplorePostCard({super.key, required this.postData});

  @override
  Widget build(BuildContext context) {
    final imageUrl =
        postData['imageUrl'] ??
        'https://via.placeholder.com/400.png?text=No+Image';
    final title = postData['title'] ?? 'Untitled Post';

    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Stack(
        fit: StackFit.expand, // Make the Stack fill the Card
        children: [
          RobustNetworkImage(
            imageUrl: imageUrl,
            fit: BoxFit.cover,
            showLoadingIndicator: false,
          ),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
