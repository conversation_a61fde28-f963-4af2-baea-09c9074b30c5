import 'package:flutter/material.dart';
import 'robust_network_image.dart';

class ImagePostView extends StatelessWidget {
  final String imageUrl;
  const ImagePostView({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: Center(
        child: RobustNetworkImage(
          imageUrl: imageUrl,
          fit: BoxFit.contain,
          errorWidget: Container(
            color: Colors.grey[800],
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.broken_image, size: 60, color: Colors.white),
                SizedBox(height: 16),
                Text(
                  'Image not found',
                  style: TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
