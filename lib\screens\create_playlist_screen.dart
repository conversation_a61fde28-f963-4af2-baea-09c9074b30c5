import 'package:flutter/material.dart';
import 'package:neubrutalism_ui/neubrutalism_ui.dart';
import 'package:wicker/services/playlist_service.dart';

class CreatePlaylistScreen extends StatefulWidget {
  const CreatePlaylistScreen({super.key});

  @override
  _CreatePlaylistScreenState createState() => _CreatePlaylistScreenState();
}

class _CreatePlaylistScreenState extends State<CreatePlaylistScreen> {
  final _nameController = TextEditingController();
  final PlaylistService _playlistService = PlaylistService();
  bool _isPrivate = false;
  bool _isLoading = false;

  void _submitPlaylist() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name for your playlist')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final message = await _playlistService.createPlaylist(
        name: _nameController.text,
        isPrivate: _isPrivate,
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message)));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString().replaceFirst('Exception: ', ''))),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Playlist'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // UPDATED: NeuTextFormField is now NeuTextField
            NeuTextField(
              controller: _nameController,
              hintText: 'Playlist Name',
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                // UPDATED: NeuSwitch syntax is slightly different
                NeuSwitch(
                  height: 40,
                  width: 80,
                  onChanged: (value) => setState(() => _isPrivate = value),
                  // No 'value' property needed, it's managed internally
                ),
                const SizedBox(width: 12),
                const Text(
                  'Make this playlist private',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ],
            ),
            const Spacer(),
            // UPDATED: NeuButton syntax has changed
            NeuButton(
              onPressed: _isLoading ? null : _submitPlaylist,
              buttonColor: Colors.teal,
              buttonHeight: 60,
              // The child is now directly inside the NeuButton
              child: Center(
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'Create Playlist',
                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}